# UW EDW Query Scheduler Guide

## 📅 Schedule Configuration

Your UW EDW query is now configured to run **every hour from 7:00 AM to 7:00 PM Pacific Time**, Monday through Sunday.

### Execution Times (Pacific Time)
- 7:00 AM, 8:00 AM, 9:00 AM, 10:00 AM, 11:00 AM
- 12:00 PM, 1:00 PM, 2:00 PM, 3:00 PM, 4:00 PM
- 5:00 PM, 6:00 PM, 7:00 PM

**Total**: 13 executions per day

## 🛠️ Management Commands

### Check Status
```bash
./check_scheduler.sh
```
Shows current status, next execution times, and recent logs.

### Install/Reinstall Scheduler
```bash
./setup_scheduler.sh
```
Installs or reinstalls the scheduler configuration.

### Uninstall Scheduler
```bash
./uninstall_scheduler.sh
```
Completely removes the scheduler.

### Manual Test Run
```bash
./run_edw_query.sh
```
Runs the query manually (respects business hours).

## 📊 Monitoring

### Log Files
- **`logs/scheduler.log`** - Main scheduler activity log
- **`logs/launchd.log`** - System scheduler output
- **`logs/launchd_error.log`** - System scheduler errors
- **`logs/edw_query.log`** - Application execution logs

### Output Files
- **`output/student_data_YYYYMMDD_HHMMSS.csv`** - Query results
- New file created each successful execution

### Check Recent Activity
```bash
# View recent scheduler logs
tail -f logs/scheduler.log

# View recent application logs
tail -f logs/edw_query.log

# List recent output files
ls -lt output/student_data_*.csv | head -5
```

## 🔧 Technical Details

### Timezone Handling
- All scheduling uses **Pacific Time** (PDT/PST)
- Automatically adjusts for daylight saving time
- Business hours check prevents execution outside 7AM-7PM

### System Integration
- Uses macOS `launchd` (preferred over cron)
- Runs as user agent (no admin privileges required)
- Survives system restarts and user logouts

### Error Handling
- Skips execution outside business hours
- Logs all activities with timestamps
- Preserves exit codes for debugging

## 🚨 Troubleshooting

### Scheduler Not Running
```bash
# Check if loaded
launchctl list | grep com.uw.edw.query

# Reload if needed
./uninstall_scheduler.sh
./setup_scheduler.sh
```

### No Query Execution
1. Check if within business hours (7AM-7PM Pacific)
2. Verify network connectivity to UW
3. Check Kerberos ticket status: `klist`
4. Review error logs: `tail logs/launchd_error.log`

### Authentication Issues
```bash
# Refresh Kerberos ticket
kinit a_ssw_edw_access

# Test manual execution
./run_edw_query.sh
```

### Permission Issues
```bash
# Ensure scripts are executable
chmod +x *.sh

# Check file permissions
ls -la logs/ output/
```

## 📈 Expected Behavior

### Normal Operation
- 13 executions per day (7AM-7PM Pacific)
- Each execution creates a timestamped CSV file
- Logs show successful completion
- No executions outside business hours

### File Growth
- **CSV files**: ~20KB per file (44 students)
- **Log files**: ~1KB per execution
- **Daily storage**: ~300KB total

### Maintenance
- Log files will grow over time
- Consider log rotation for long-term use
- Monitor disk space in `output/` directory

## 🔒 Security Notes

- Scheduler runs with your user permissions
- Kerberos tickets may expire (typically 10 hours)
- Network connectivity required to UW systems
- No sensitive data stored in configuration files

## 📞 Support

### Quick Diagnostics
```bash
# Full status check
./check_scheduler.sh

# Test database connection
./venv/bin/python test_setup.py

# Manual query execution
./venv/bin/python edw_query.py
```

### Common Solutions
1. **No data returned**: Check query date parameters in `studentquery.sql`
2. **Authentication failed**: Run `kinit a_ssw_edw_access`
3. **Network issues**: Verify UW VPN connection
4. **Scheduler stopped**: Run `./setup_scheduler.sh` to reinstall

Your scheduler is now active and will begin executing queries at the next scheduled hour within business hours (7AM-7PM Pacific)!
