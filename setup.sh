#!/bin/bash

# UW EDW Query Setup Script
# This script sets up the environment for querying the UW Enterprise Data Warehouse

echo "Setting up UW EDW Query Environment..."

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "Warning: This script is designed for macOS. Some steps may not work on other systems."
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install Python dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

# Create necessary directories
echo "Creating directories..."
mkdir -p output
mkdir -p logs

# Check for Microsoft ODBC Driver
echo "Checking for Microsoft ODBC Driver for SQL Server..."
if ! odbcinst -q -d | grep -q "ODBC Driver.*for SQL Server"; then
    echo "WARNING: Microsoft ODBC Driver for SQL Server not found!"
    echo "Please install it using one of these methods:"
    echo ""
    echo "Method 1 - Using Homebrew:"
    echo "  brew tap microsoft/mssql-release https://github.com/Microsoft/homebrew-mssql-release"
    echo "  brew update"
    echo "  HOMEBREW_ACCEPT_EULA=Y brew install msodbcsql18 mssql-tools18"
    echo ""
    echo "Method 2 - Download from Microsoft:"
    echo "  Visit: https://docs.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server"
    echo ""
else
    echo "Microsoft ODBC Driver found!"
fi

# Check for kinit
echo "Checking for Kerberos kinit..."
if command -v kinit &> /dev/null; then
    echo "kinit found at: $(which kinit)"
else
    echo "ERROR: kinit not found! Please install Kerberos client tools."
    exit 1
fi

# Make the main script executable
chmod +x edw_query.py

echo ""
echo "Setup completed!"
echo ""
echo "Next steps:"
echo "1. Review and update the .env file with your specific settings"
echo "2. Test the connection: python3 edw_query.py"
echo "3. Set up cron job for hourly execution (see README.md)"
echo ""
echo "To activate the virtual environment in the future, run:"
echo "  source venv/bin/activate"
