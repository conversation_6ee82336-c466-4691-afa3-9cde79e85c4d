#!/bin/bash

# Test script for service account authentication and database access

echo "Testing Service Account Authentication"
echo "===================================="

# Set working directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

# Load environment variables
if [ -f ".env" ]; then
    source .env
else
    echo "ERROR: .env file not found"
    exit 1
fi

echo "Service Account: $UW_NETID"
echo "Current Time: $(date)"
echo ""

# Check if password file exists
if [ ! -f ".service_password" ]; then
    echo "❌ Service account password file not found"
    echo "Please run: ./setup_service_account.sh"
    exit 1
fi

# Show current tickets
echo "Current Kerberos Tickets:"
if klist 2>/dev/null; then
    echo ""
else
    echo "No current tickets"
    echo ""
fi

# Clear existing tickets
echo "Clearing existing tickets..."
kdestroy 2>/dev/null || true

# Test authentication
echo "Testing service account authentication..."
SERVICE_PASSWORD=$(cat .service_password)

# Create temporary expect script to handle special characters properly
cat > /tmp/kinit_expect_$$.exp << 'EXPECTEOF'
#!/usr/bin/expect
set timeout 10
set service_account [lindex $argv 0]
set service_password [lindex $argv 1]

spawn kinit $service_account
expect {
    "password:" {
        send "$service_password\r"
        exp_continue
    }
    eof
}
EXPECTEOF

chmod +x /tmp/kinit_expect_$$.exp
/tmp/kinit_expect_$$.exp "$UW_NETID" "$SERVICE_PASSWORD"
rm -f /tmp/kinit_expect_$$.exp

# Verify authentication
if klist -s 2>/dev/null; then
    echo "✅ Authentication successful!"
    echo ""
    echo "New Kerberos Tickets:"
    klist
    echo ""
    
    # Test database connection
    echo "Testing database connection..."
    echo "=============================="
    
    # Activate virtual environment and run query
    source venv/bin/activate
    python3 edw_query.py
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ Database connection and query execution successful!"
        echo ""
        echo "Recent output files:"
        ls -lt output/student_data_*.csv | head -3
    else
        echo ""
        echo "❌ Database connection or query execution failed"
        echo "Check logs/edw_query.log for details"
    fi
    
else
    echo "❌ Authentication failed!"
    echo ""
    echo "Please check:"
    echo "1. Service account password"
    echo "2. Network connectivity"
    echo "3. Account status"
    exit 1
fi
