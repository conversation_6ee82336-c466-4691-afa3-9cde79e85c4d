#!/bin/bash

# Wrapper script for running EDW query with service account authentication
# This script ensures proper environment setup and respects Pacific time business hours

# Set the working directory to the script's location
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

# Set timezone to Pacific
export TZ="America/Los_Angeles"

# Get current hour in Pacific time (0-23)
CURRENT_HOUR=$(date +%H)

# Check if current time is between 7AM (07) and 7PM (19) Pacific
if [ "$CURRENT_HOUR" -lt 7 ] || [ "$CURRENT_HOUR" -gt 19 ]; then
    echo "$(date): Skipping execution - outside business hours (7AM-7PM Pacific). Current hour: $CURRENT_HOUR" >> logs/scheduler.log
    exit 0
fi

# Log start time
echo "$(date): Starting EDW query execution (Hour: $CURRENT_HOUR Pacific)" >> logs/scheduler.log

# Load environment variables
source .env

# Function to check if we have a valid service account ticket
check_service_ticket() {
    if klist -s 2>/dev/null; then
        # Check if the current principal matches our service account
        current_principal=$(klist | grep "Default principal:" | awk '{print $3}' 2>/dev/null)
        if [[ "$current_principal" == "${UW_NETID}@"* ]]; then
            echo "$(date): Valid service account ticket found for $current_principal" >> logs/scheduler.log
            return 0
        fi
    fi
    return 1
}

# Function to authenticate with service account using stored password
authenticate_service_account() {
    echo "$(date): Authenticating with service account: $UW_NETID" >> logs/scheduler.log

    # Check if password file exists
    if [ ! -f ".service_password" ]; then
        echo "$(date): ERROR - Service account password file not found" >> logs/scheduler.log
        return 1
    fi

    # Read password from secure file
    SERVICE_PASSWORD=$(cat .service_password)

    # Create temporary expect script to handle special characters properly
    cat > /tmp/kinit_expect_$$.exp << 'EXPECTEOF'
#!/usr/bin/expect
set timeout 10
set service_account [lindex $argv 0]
set service_password [lindex $argv 1]

spawn kinit $service_account
expect {
    "password:" {
        send "$service_password\r"
        exp_continue
    }
    eof
}
EXPECTEOF

    chmod +x /tmp/kinit_expect_$$.exp
    /tmp/kinit_expect_$$.exp "$UW_NETID" "$SERVICE_PASSWORD" >> logs/scheduler.log 2>&1
    rm -f /tmp/kinit_expect_$$.exp

    # Check if authentication was successful
    if klist -s 2>/dev/null; then
        echo "$(date): Service account authentication successful" >> logs/scheduler.log
        return 0
    else
        echo "$(date): ERROR - Service account authentication failed" >> logs/scheduler.log
        return 1
    fi
}

# Ensure we have valid service account credentials
if ! check_service_ticket; then
    echo "$(date): No valid service account ticket found, attempting authentication" >> logs/scheduler.log
    if ! authenticate_service_account; then
        echo "$(date): FATAL - Could not authenticate service account" >> logs/scheduler.log
        exit 1
    fi
fi

# Activate virtual environment
source venv/bin/activate

# Run the Python script
python3 edw_query.py

# Capture exit code
EXIT_CODE=$?

# Log completion
if [ $EXIT_CODE -eq 0 ]; then
    echo "$(date): EDW query completed successfully" >> logs/scheduler.log
else
    echo "$(date): EDW query failed with exit code $EXIT_CODE" >> logs/scheduler.log
fi

exit $EXIT_CODE
