#!/bin/bash

# Wrapper script for running EDW query via cron
# This script ensures proper environment setup for automated execution

# Set the working directory to the script's location
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

# Log start time
echo "$(date): Starting EDW query execution" >> logs/cron.log

# Activate virtual environment
source venv/bin/activate

# Run the Python script
python3 edw_query.py

# Capture exit code
EXIT_CODE=$?

# Log completion
if [ $EXIT_CODE -eq 0 ]; then
    echo "$(date): EDW query completed successfully" >> logs/cron.log
else
    echo "$(date): EDW query failed with exit code $EXIT_CODE" >> logs/cron.log
fi

exit $EXIT_CODE
