#!/bin/bash

# Wrapper script for running EDW query with time-based scheduling
# This script ensures proper environment setup and respects Pacific time business hours

# Set the working directory to the script's location
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

# Set timezone to Pacific
export TZ="America/Los_Angeles"

# Get current hour in Pacific time (0-23)
CURRENT_HOUR=$(date +%H)

# Check if current time is between 7AM (07) and 7PM (19) Pacific
if [ "$CURRENT_HOUR" -lt 7 ] || [ "$CURRENT_HOUR" -gt 19 ]; then
    echo "$(date): Skipping execution - outside business hours (7AM-7PM Pacific). Current hour: $CURRENT_HOUR" >> logs/scheduler.log
    exit 0
fi

# Log start time
echo "$(date): Starting EDW query execution (Hour: $CURRENT_HOUR Pacific)" >> logs/scheduler.log

# Activate virtual environment
source venv/bin/activate

# Run the Python script
python3 edw_query.py

# Capture exit code
EXIT_CODE=$?

# Log completion
if [ $EXIT_CODE -eq 0 ]; then
    echo "$(date): EDW query completed successfully" >> logs/scheduler.log
else
    echo "$(date): EDW query failed with exit code $EXIT_CODE" >> logs/scheduler.log
fi

exit $EXIT_CODE
