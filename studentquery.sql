Use UWSDBDataStore

DECLARE @AutumnQuarter int, @Year int
SET @AutumnQuarter = 4
SET @Year = 2025

SELECT 
	 S1.student_name_lowc
	 ,IIF(S1.student_name_lowc NOT LIKE '%,%', LEFT(S1.student_name_lowc, CHARINDEX(',', S1.student_name_lowc, 0)), LEFT(S1.student_name_lowc, CHARINDEX(',', S1.student_name_lowc, 0) - 1)) as Lastname
	 ,RIGHT(RTRIM(S1.student_name_lowc), LEN(RTRIM(S1.student_name_lowc)) - CHARINDEX(',', S1.student_name_lowc, 0)) as FirstName
	 ,RTRIM(S1.student_name_pn_l) as PreferredLastName
	 ,RTRIM(S1.student_name_pn_f) as PreferredFirstName
	 ,RTRIM(S1.student_name_pn_m) as PreferredMiddleName
	 ,RTRIM(ADDR.e_mail_ucs) as UWEmail
	 ,RTRIM(ADDR.e_mail_other) as ExternalEMail
	 
	, RIGHT('0000000' + CONVERT(VA<PERSON>HAR, S1.student_no), 7) as <PERSON><PERSON><PERSON><PERSON>
	, S1.system_key as SDBSystemKey
	,CASE 
		WHEN S1.last_qtr_enrolled = 1
			THEN 'Winter ' + CAST(S1.last_yr_enrolled AS CHAR)
		WHEN S1.last_qtr_enrolled = 2
			THEN 'Spring ' + CAST(S1.last_yr_enrolled AS CHAR)
		WHEN S1.last_qtr_enrolled = 3
			THEN 'Summer ' + CAST(S1.last_yr_enrolled AS CHAR)
		WHEN S1.last_qtr_enrolled = 4
			THEN 'Autumn ' + CAST(S1.last_yr_enrolled AS CHAR)
		END as LastEnrolled
	,SCM.major_abbr
	,RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) as [Student Pathway]
	
	, 
		CASE 
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC W 00-20%' THEN 'Unknown'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC W 00-27%' THEN 'DAY 2 yr'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC WF 00-11%' THEN 'BASW	'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC WL 00-30%' THEN 'Pre-Doctor'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC WL 00-41%' THEN 'Doctor'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWAP 00-27%' THEN 'DAY 2 yr'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWAP 010-27%' THEN 'DAY Adv St'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCC 00-27%' THEN 'DAY 2 yr'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCC 010-27%' THEN 'DAY Adv St'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCL 00-27%' THEN 'DAY 2 yr'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCL 010-27%' THEN 'DAY Adv St'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWEC 00-27%' THEN 'EDP 3 yr'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWEC 010-27%' THEN 'EDP Adv St'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%T SOCW 20-27%' THEN 'T MSW Standard'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%T SOCW 210-27%' THEN 'T MSW Adv St'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%TSOCWF 00-11%' THEN 'T BASW'
		ELSE 'Not Provided' END
		as [StudentTypePerPathway]

	, 
		CASE 
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC W 00-27%' THEN 'MSW Day'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC WF 00-11%' THEN 'BASW'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC WL 00-30%' THEN 'PHD'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC WL 00-41%' THEN 'PHD'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWAP 00-27%' THEN 'MSW Day'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWAP 010-27%' THEN 'MSW Day'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCC 00-27%' THEN 'MSW Day'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCC 010-27%' THEN 'MSW Day'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCL 00-27%' THEN 'MSW Day'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCL 010-27%' THEN 'MSW Day'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWEC 00-27%' THEN 'MSW EDP'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWEC 010-27%' THEN 'MSW EDP'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%T SOCW 20-27%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%T SOCW 210-27%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%TSOCWF 00-11%' THEN 'N/A'
		ELSE 'Not Provided' END
		as [StudentProgram]

	, 
		CASE 
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC W 00-27%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC WF 00-11%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC WL 00-30%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC WL 00-41%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWAP 00-27%' THEN 'APP'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWAP 010-27%' THEN 'APP'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCC 00-27%' THEN 'CCIP'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCC 010-27%' THEN 'CCIP'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCL 00-27%' THEN 'Clinical'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCL 010-27%' THEN 'Clinical'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWEC 00-27%' THEN 'Clinical'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWEC 010-27%' THEN 'Clinical'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%T SOCW 20-27%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%T SOCW 210-27%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%TSOCWF 00-11%' THEN 'N/A'
		ELSE 'Blank' END
		as [StudentSpecialization]

, 
		CASE 
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC W 00-27%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC WF 00-11%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC WL 00-30%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOC WL 00-41%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWAP 00-27%' THEN 'No'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWAP 010-27%' THEN 'Yes'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCC 00-27%' THEN 'No'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCC 010-27%' THEN 'Yes'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCL 00-27%' THEN 'No'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWCL 010-27%' THEN 'Yes'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWEC 00-27%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%SOCWEC 010-27%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%T SOCW 20-27%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%T SOCW 210-27%' THEN 'N/A'
		WHEN RTRIM(LTRIM(CAST(SCM.Major_abbr as varchar(20)))) + ' ' + CAST(SCM.branch as varchar(20)) + CAST(SCM.pathway as varchar(20)) + '-' + CAST(SCM.deg_level as varchar(20)) + CAST(SCM.deg_type as varchar(20)) Like '%TSOCWF 00-11%' THEN 'N/A'
		ELSE 'Blank' END
		as [StudentAdvancedStanding]

	,CASE 
		WHEN SCM.College LIKE '%T%'  THEN 'uwsocialwork.campus.2229'
		WHEN SCM.College LIKE '%Z%' THEN 'uwsocialwork.campus.2777' 
		ELSE 'Unknown' END as [CampusPerMajor] 

	,CASE 
		WHEN S1.Class LIKE '0'  THEN 'Pending'
		WHEN S1.Class LIKE '1'  THEN 'Freshman'
		WHEN S1.Class LIKE '2'  THEN 'Sophomore'
		WHEN S1.Class LIKE '3'  THEN 'Junior'
		WHEN S1.Class LIKE '4'  THEN 'Senior'
		WHEN S1.Class LIKE '5'  THEN 'Post-Baccalaureate'
		WHEN S1.Class LIKE '6'  THEN 'Non-Matriculated'
		WHEN S1.Class LIKE '8'  THEN 'Graduate'
		WHEN S1.Class LIKE '11'  THEN '1st Year Professional'
		WHEN S1.Class LIKE '12'  THEN '2nd Year Professional'
		WHEN S1.Class LIKE '13'  THEN '3rd Year Professional'		
		WHEN S1.Class LIKE '14'  THEN '4th Year Professional'
		ELSE 'Unknown' END as [StudentClass]

	,CASE 
		WHEN S1.projected_class LIKE '0'  THEN 'Pending'
		WHEN S1.projected_class LIKE '1'  THEN 'Freshman'
		WHEN S1.projected_class LIKE '2'  THEN 'Sophomore'
		WHEN S1.projected_class LIKE '3'  THEN 'Junior'
		WHEN S1.projected_class LIKE '4'  THEN 'Senior'
		WHEN S1.projected_class LIKE '5'  THEN 'Post-Baccalaureate'
		WHEN S1.projected_class LIKE '6'  THEN 'Non-Matriculated'
		WHEN S1.projected_class LIKE '8'  THEN 'Graduate'
		WHEN S1.projected_class LIKE '11'  THEN '1st Year Professional'
		WHEN S1.projected_class LIKE '12'  THEN '2nd Year Professional'
		WHEN S1.projected_class LIKE '13'  THEN '3rd Year Professional'		
		WHEN S1.projected_class LIKE '14'  THEN '4th Year Professional'
		ELSE 'Unknown' END as [StudentProjectedClass]
	,CASE 
		WHEN SCM.major_abbr LIKE 'SOC%'  THEN 'uwsocialwork.campus.2229'
		WHEN SCM.major_abbr LIKE 'T%' THEN 'uwsocialwork.campus.2777' 
		ELSE 'Unknown' END  
	AS Campus
	,CASE 
			WHEN SCM.major_abbr = 'SOC W'  THEN 'MSW Day Foundation'
			WHEN SCM.major_abbr = 'SOC WF' THEN 'BASW' 
			WHEN SCM.major_abbr = 'SOC WL' THEN 'PhD' 
			WHEN SCM.major_abbr = 'SOCWAP' THEN 'MSW DAY Advanced Standing' 
			WHEN SCM.major_abbr = 'SOCWCC' THEN 'MSW DAY Advanced CCIP' 
			WHEN SCM.major_abbr = 'SOCWCL' THEN 'MSW Day Advanced Clin' 
			WHEN SCM.major_abbr = 'SOCWEC' THEN 'MSW Day Foundation'  
			WHEN SCM.major_abbr = 'SOCWEC' THEN 'MSW EDP'
			WHEN SCM.major_abbr = 'TSOCWF' THEN 'BASW' 
			WHEN SCM.major_abbr = 'T SOCW' THEN 'MSW'
			ELSE 'Unknown' END  
		AS StudentType

	,CASE 
		WHEN EXISTS (SELECT 1 FROM sec.registration_courses REG WHERE REG.regis_yr = @Year AND REG.regis_qtr = @AutumnQuarter AND REG.crs_number = 569 AND S1.system_key = REG.System_key AND REG.crs_curric_abbr NOT LIKE 'T%' AND SCM.major_abbr NOT LIKE 'SOC WL') THEN 'Day Advanced, CCIP Year 2 or Advanced Standing' 
		WHEN EXISTS (SELECT 1 FROM sec.registration_courses REG WHERE REG.regis_yr = @Year AND REG.regis_qtr = @AutumnQuarter AND REG.crs_number = 504 AND S1.system_key = REG.System_key AND REG.crs_curric_abbr NOT LIKE 'T%' AND SCM.major_abbr NOT LIKE 'SOC WL') THEN 'Day Foundation Year 1' 
		WHEN EXISTS (SELECT 1 FROM sec.registration_courses REG WHERE REG.regis_yr = @Year AND REG.regis_qtr = @AutumnQuarter AND REG.crs_number = 550 AND S1.system_key = REG.System_key AND REG.crs_curric_abbr NOT LIKE 'T%' AND SCM.major_abbr NOT LIKE 'SOC WL') THEN 'Day Advanced, APP Year 2 or Advanced Standing' 
		WHEN EXISTS (SELECT 1 FROM sec.registration_courses REG WHERE REG.regis_yr = @Year AND REG.regis_qtr = @AutumnQuarter AND REG.crs_number = 560 AND S1.system_key = REG.System_key AND REG.crs_curric_abbr NOT LIKE 'T%' AND SCM.major_abbr NOT LIKE 'SOC WL') THEN 'Day Advanced, APP Year 2 or Advanced Standing'  
		WHEN EXISTS (SELECT 1 FROM sec.registration_courses REG WHERE REG.regis_yr = @Year AND REG.regis_qtr = @AutumnQuarter AND REG.crs_number = 514 AND S1.system_key = REG.System_key AND REG.crs_curric_abbr NOT LIKE 'T%' AND SCM.major_abbr NOT LIKE 'SOC WL') THEN 'Day Advanced Clin Year 2 or Advanced Standing' 
		WHEN EXISTS (SELECT 1 FROM sec.registration_courses REG WHERE REG.regis_yr = @Year AND REG.regis_qtr = @AutumnQuarter AND REG.crs_number = 515 AND S1.system_key = REG.System_key AND REG.crs_curric_abbr NOT LIKE 'T%' AND SCM.major_abbr NOT LIKE 'SOC WL') THEN 'Day Advanced Clin Year 2 or Advanced Standing'
		    
		WHEN EXISTS (SELECT 1 FROM sec.registration_courses REG WHERE REG.regis_yr = @Year AND REG.regis_qtr = @AutumnQuarter AND REG.crs_number = 500 AND S1.system_key = REG.System_key AND REG.crs_curric_abbr NOT LIKE 'T%' AND SCM.major_abbr NOT LIKE 'SOC WL') THEN 'EDP Year 1'
		WHEN EXISTS (SELECT 1 FROM sec.registration_courses REG WHERE REG.regis_yr = @Year AND REG.regis_qtr = @AutumnQuarter AND REG.crs_number = 501 AND S1.system_key = REG.System_key AND REG.crs_curric_abbr NOT LIKE 'T%' AND SCM.major_abbr NOT LIKE 'SOC WL') THEN 'EDP Year 1'
		WHEN EXISTS (SELECT 1 FROM sec.registration_courses REG WHERE REG.regis_yr = @Year AND REG.regis_qtr = @AutumnQuarter AND REG.crs_number = 505 AND S1.system_key = REG.System_key AND REG.crs_curric_abbr NOT LIKE 'T%' AND SCM.major_abbr NOT LIKE 'SOC WL') THEN 'EDP Year 2'
		WHEN EXISTS (SELECT 1 FROM sec.registration_courses REG WHERE REG.regis_yr = @Year AND REG.regis_qtr = @AutumnQuarter AND REG.crs_number = 571 AND S1.system_key = REG.System_key AND REG.crs_curric_abbr NOT LIKE 'T%' AND SCM.major_abbr NOT LIKE 'SOC WL') THEN 'EDP, Possible Advanced Standing'

		WHEN EXISTS (SELECT 1 FROM sec.registration_courses REG WHERE REG.regis_yr = @Year AND REG.regis_qtr = @AutumnQuarter AND REG.crs_number = 310 AND S1.system_key = REG.System_key AND REG.crs_curric_abbr = 'SOC WF' AND SCM.major_abbr NOT LIKE 'SOC WL') THEN 'BASW Year 1'
		WHEN EXISTS (SELECT 1 FROM sec.registration_courses REG WHERE REG.regis_yr = @Year AND REG.regis_qtr = @AutumnQuarter AND REG.crs_number = 405 AND S1.system_key = REG.System_key AND REG.crs_curric_abbr = 'SOC WF' AND SCM.major_abbr NOT LIKE 'SOC WL') THEN 'BASW Year 2'

		WHEN SCM.major_abbr = 'TSOCWF' THEN 'Tacoma Full Time'
		WHEN SCM.major_abbr = 'T SOCW' THEN 'Tacoma Part Time EDP'
		ELSE 'Uknown'
	END as StudentDescBasedOnRegistration

	, s1_gender as Gender
	,CASE 
		WHEN sex = 1  THEN 'Female'
		WHEN sex = 0  THEN 'Male' 
		END  
	AS Sex
	,'N/A' as GenderPronouns
	,s1.Ethnic_code, ETH.ethnic_desc
	,CASE WHEN S1.hispanic_code <> 999 THEN 'HISPANIC' ELSE '' END as Hispanic
	,CASE 
		WHEN S1.last_qtr_enrolled = 1 THEN 'Winter'
		WHEN S1.last_qtr_enrolled = 2 THEN 'Spring' 
		WHEN S1.last_qtr_enrolled = 3 THEN 'Summer' 
		WHEN S1.last_qtr_enrolled = 4 THEN 'Autumn' 
		ELSE 'Unknown' END  
		AS LastQuarterEnrolled

	, RTRIM(LTRIM(ADDR.local_line_1)) as AddressLine1
	, RTRIM(LTRIM(ADDR.local_line_2)) as AddressLine2
	, RTRIM(LTRIM(ADDR.local_city)) as AddressCity
	, RTRIM(LTRIM(ADDR.local_zip_5)) as AddressRegionZip
	, RTRIM(LTRIM(ADDR.local_state)) as AddressStateAbbreviation
	, RTRIM(LTRIM(ADDR.local_country)) as AddressCountryName 
	, CASE WHEN ADDR.local_phone_num > 1000000000 THEN '(' + LEFT(CAST(ADDR.local_phone_num AS CHAR(10)), 3) + ') ' + SUBSTRING(CAST(ADDR.local_phone_num AS CHAR(10)), 4, 3) + '-' + RIGHT(CAST(ADDR.local_phone_num AS CHAR(10)), 4) ELSE '' END as Phone

	,CASE 
			WHEN S1.resident = 0  THEN 'UNKNOWN'
			WHEN S1.resident = 1 THEN 'RESIDENT' 
			WHEN S1.resident = 2 THEN 'RESIDENT IMMIGRANT' 
			WHEN S1.resident = 3 THEN 'NONRESIDENT CITIZEN' 
			WHEN S1.resident = 4 THEN 'NONRESIDENT IMMIGRANT' 
			WHEN S1.resident = 5 THEN 'NONRESIDENT STUDENT VISA' 
			WHEN S1.resident = 6 THEN 'NONCITIZEN OTHER'
			ELSE 'Unknown' END 
			AS Residency
			,'N/A' as EnglishSpeaker
			,'N/A' as USCitizen

	FROM 
	sec.student_1 S1 
	INNER JOIN sec.student_1_college_major SCM ON SCM.system_key = S1.system_key
	INNER JOIN sec.sys_tbl_21_ethnic ETH ON RIGHT(CONVERT(VARCHAR, table_key),3) = CONVERT(VARCHAR,s1.Ethnic_code)
	INNER JOIN sec.addresses ADDR ON ADDR.system_key = S1.system_key

	WHERE 
		1 = 1
		AND S1.last_yr_enrolled = @Year 
		AND S1.last_qtr_enrolled = @AutumnQuarter
		AND SCM.major_abbr IN ('SOC W' ,'SOC WF','SOC WL', 'SOCWAP','SOCWCC', 'SOCWCL', 'SOCWEC', 'TSOCWF', 'T SOCW')

	ORDER BY SCM.major_abbr, S1.student_name_lowc