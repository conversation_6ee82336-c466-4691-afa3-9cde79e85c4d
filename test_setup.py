#!/usr/bin/env python3
"""
Test script to verify the EDW query setup
Checks all dependencies and configuration before running the main script
"""

import os
import sys
import subprocess
import importlib
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✓ Python version: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"✗ Python version {version.major}.{version.minor}.{version.micro} is not supported. Need Python 3.8+")
        return False

def check_python_packages():
    """Check if required Python packages are installed."""
    packages = ['pyodbc', 'dotenv']
    all_installed = True
    
    for package in packages:
        try:
            if package == 'dotenv':
                importlib.import_module('dotenv')
                print(f"✓ Python package: python-dotenv")
            else:
                importlib.import_module(package)
                print(f"✓ Python package: {package}")
        except ImportError:
            print(f"✗ Python package missing: {package}")
            all_installed = False
    
    return all_installed

def check_kinit():
    """Check if kinit is available."""
    try:
        result = subprocess.run(['which', 'kinit'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ kinit found at: {result.stdout.strip()}")
            return True
        else:
            print("✗ kinit not found")
            return False
    except Exception as e:
        print(f"✗ Error checking kinit: {e}")
        return False

def check_odbc_driver():
    """Check if Microsoft ODBC driver is installed."""
    try:
        # Try to import pyodbc and list drivers
        import pyodbc
        drivers = pyodbc.drivers()
        
        mssql_drivers = [d for d in drivers if 'SQL Server' in d and 'ODBC Driver' in d]
        
        if mssql_drivers:
            print(f"✓ ODBC drivers found: {', '.join(mssql_drivers)}")
            return True
        else:
            print("✗ Microsoft ODBC Driver for SQL Server not found")
            print("  Available drivers:", ', '.join(drivers) if drivers else "None")
            return False
            
    except ImportError:
        print("✗ Cannot check ODBC drivers - pyodbc not installed")
        return False
    except Exception as e:
        print(f"✗ Error checking ODBC drivers: {e}")
        return False

def check_files():
    """Check if required files exist."""
    required_files = [
        'studentquery.sql',
        '.env',
        'requirements.txt',
        'edw_query.py'
    ]
    
    all_exist = True
    for file in required_files:
        if Path(file).exists():
            print(f"✓ File exists: {file}")
        else:
            print(f"✗ File missing: {file}")
            all_exist = False
    
    return all_exist

def check_directories():
    """Check if required directories exist or can be created."""
    directories = ['output', 'logs']
    
    for directory in directories:
        dir_path = Path(directory)
        if dir_path.exists():
            print(f"✓ Directory exists: {directory}")
        else:
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"✓ Directory created: {directory}")
            except Exception as e:
                print(f"✗ Cannot create directory {directory}: {e}")
                return False
    
    return True

def check_environment_variables():
    """Check if .env file has required variables."""
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        required_vars = [
            'EDW_SERVER',
            'EDW_DATABASE', 
            'UW_NETID',
            'SQL_QUERY_FILE'
        ]
        
        all_set = True
        for var in required_vars:
            value = os.getenv(var)
            if value:
                print(f"✓ Environment variable: {var} = {value}")
            else:
                print(f"✗ Environment variable missing: {var}")
                all_set = False
        
        return all_set
        
    except Exception as e:
        print(f"✗ Error checking environment variables: {e}")
        return False

def main():
    """Run all setup checks."""
    print("UW EDW Query Setup Verification")
    print("=" * 40)
    
    checks = [
        ("Python Version", check_python_version),
        ("Python Packages", check_python_packages),
        ("Kerberos (kinit)", check_kinit),
        ("ODBC Driver", check_odbc_driver),
        ("Required Files", check_files),
        ("Directories", check_directories),
        ("Environment Variables", check_environment_variables)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n{check_name}:")
        result = check_func()
        results.append((check_name, result))
    
    print("\n" + "=" * 40)
    print("SUMMARY:")
    
    all_passed = True
    for check_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{check_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n✓ All checks passed! You can now run: python3 edw_query.py")
        return 0
    else:
        print("\n✗ Some checks failed. Please address the issues above.")
        print("\nTo install missing components:")
        print("1. Microsoft ODBC Driver: brew install msodbcsql18")
        print("2. Python packages: pip install -r requirements.txt")
        return 1

if __name__ == "__main__":
    sys.exit(main())
