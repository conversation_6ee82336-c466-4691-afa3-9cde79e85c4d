#!/bin/bash

# Setup script for UW EDW Service Account Authentication
# Securely stores service account password and tests authentication

echo "UW EDW Service Account Setup"
echo "============================"

# Set working directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

# Load environment variables
if [ -f ".env" ]; then
    source .env
else
    echo "ERROR: .env file not found"
    exit 1
fi

echo "Service Account: $UW_NETID"
echo ""

# Check if password file already exists
if [ -f ".service_password" ]; then
    echo "Service account password file already exists."
    read -p "Do you want to update it? (y/N): " update_password
    if [[ ! "$update_password" =~ ^[Yy]$ ]]; then
        echo "Using existing password file."
        test_auth=true
    else
        rm .service_password
        test_auth=false
    fi
else
    test_auth=false
fi

# Get password if needed
if [ ! -f ".service_password" ]; then
    echo "Please enter the password for $UW_NETID:"
    read -s SERVICE_PASSWORD
    echo ""
    
    # Validate password is not empty
    if [ -z "$SERVICE_PASSWORD" ]; then
        echo "ERROR: Password cannot be empty"
        exit 1
    fi
    
    # Store password securely
    echo "$SERVICE_PASSWORD" > .service_password
    chmod 600 .service_password
    echo "Password stored securely in .service_password (permissions: 600)"
fi

# Test authentication
echo ""
echo "Testing service account authentication..."

# Clear any existing tickets first
kdestroy 2>/dev/null || true

# Read stored password
SERVICE_PASSWORD=$(cat .service_password)

# Test authentication using expect with proper escaping
cat > /tmp/kinit_expect_$$.exp << 'EXPECTEOF'
#!/usr/bin/expect
set timeout 10
set service_account [lindex $argv 0]
set service_password [lindex $argv 1]

spawn kinit $service_account
expect {
    "password:" {
        send "$service_password\r"
        exp_continue
    }
    eof
}
EXPECTEOF

chmod +x /tmp/kinit_expect_$$.exp
/tmp/kinit_expect_$$.exp "$UW_NETID" "$SERVICE_PASSWORD"
rm -f /tmp/kinit_expect_$$.exp

# Check if authentication was successful
if klist -s 2>/dev/null; then
    echo "✅ Service account authentication successful!"
    
    # Show ticket details
    echo ""
    echo "Kerberos Ticket Details:"
    klist
    
    echo ""
    echo "✅ Service account setup complete!"
    echo ""
    echo "Next steps:"
    echo "1. Test the query: ./run_edw_query.sh"
    echo "2. The scheduler will now use the service account automatically"
    echo ""
    echo "Security notes:"
    echo "- Password stored in .service_password (mode 600)"
    echo "- Add .service_password to .gitignore if using version control"
    echo "- Consider rotating the service account password periodically"
    
else
    echo "❌ Service account authentication failed!"
    echo ""
    echo "Possible issues:"
    echo "1. Incorrect password"
    echo "2. Account locked or expired"
    echo "3. Network connectivity issues"
    echo "4. Kerberos configuration problems"
    echo ""
    echo "Please verify the password and try again."
    
    # Remove the password file if authentication failed
    rm -f .service_password
    exit 1
fi
