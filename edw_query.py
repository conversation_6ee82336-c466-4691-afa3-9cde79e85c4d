#!/usr/bin/env python3
"""
UW Enterprise Data Warehouse Query Script
Connects to UW EDW using Kerberos authentication and executes SQL queries.
Designed to run hourly via cron job.
"""

import os
import sys
import subprocess
import logging
import pyodbc
import csv
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

class EDWQueryManager:
    def __init__(self):
        """Initialize the EDW Query Manager with configuration."""
        # Load environment variables
        load_dotenv()
        
        # Database configuration
        self.server = os.getenv('EDW_SERVER', 'edwpub.s.uw.edu')
        self.database = os.getenv('EDW_DATABASE', 'EDWPresentation')
        self.encrypt = os.getenv('EDW_ENCRYPT', 'yes')
        self.trust_cert = os.getenv('EDW_TRUST_SERVER_CERTIFICATE', 'no')
        
        # Authentication configuration
        self.netid = os.getenv('UW_NETID', 'a_ssw_edw_access')
        
        # Query configuration
        self.sql_file = os.getenv('SQL_QUERY_FILE', 'studentquery.sql')
        
        # Output configuration
        self.output_format = os.getenv('OUTPUT_FORMAT', 'csv')
        self.output_dir = Path(os.getenv('OUTPUT_DIRECTORY', './output'))
        self.output_prefix = os.getenv('OUTPUT_FILENAME_PREFIX', 'student_data')
        
        # Logging configuration
        self.log_level = os.getenv('LOG_LEVEL', 'INFO')
        self.log_file = os.getenv('LOG_FILE', './logs/edw_query.log')
        
        # Setup logging
        self._setup_logging()
        
        # Create output directory if it doesn't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("EDW Query Manager initialized")

    def _setup_logging(self):
        """Setup logging configuration."""
        # Create logs directory if it doesn't exist
        log_dir = Path(self.log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, self.log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def authenticate_kerberos(self):
        """Authenticate using Kerberos kinit."""
        try:
            self.logger.info(f"Authenticating with Kerberos for user: {self.netid}")
            
            # Run kinit command
            result = subprocess.run(
                ['kinit', self.netid],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                self.logger.info("Kerberos authentication successful")
                return True
            else:
                self.logger.error(f"Kerberos authentication failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.logger.error("Kerberos authentication timed out")
            return False
        except Exception as e:
            self.logger.error(f"Error during Kerberos authentication: {str(e)}")
            return False

    def check_kerberos_ticket(self):
        """Check if we have a valid Kerberos ticket."""
        try:
            result = subprocess.run(['klist'], capture_output=True, text=True)
            if result.returncode == 0 and 'krbtgt' in result.stdout:
                self.logger.info("Valid Kerberos ticket found")
                return True
            else:
                self.logger.warning("No valid Kerberos ticket found")
                return False
        except Exception as e:
            self.logger.error(f"Error checking Kerberos ticket: {str(e)}")
            return False

    def load_sql_query(self):
        """Load SQL query from file."""
        try:
            sql_path = Path(self.sql_file)
            if not sql_path.exists():
                raise FileNotFoundError(f"SQL file not found: {self.sql_file}")
            
            with open(sql_path, 'r', encoding='utf-8') as file:
                query = file.read()
            
            self.logger.info(f"SQL query loaded from: {self.sql_file}")
            return query
            
        except Exception as e:
            self.logger.error(f"Error loading SQL query: {str(e)}")
            raise

    def create_connection_string(self):
        """Create ODBC connection string for SQL Server with Kerberos."""
        # Use Microsoft ODBC Driver for SQL Server
        driver = "ODBC Driver 18 for SQL Server"
        
        connection_string = (
            f"DRIVER={{{driver}}};"
            f"SERVER={self.server};"
            f"DATABASE={self.database};"
            f"Trusted_Connection=yes;"
            f"Encrypt={self.encrypt};"
            f"TrustServerCertificate={self.trust_cert};"
        )
        
        return connection_string

    def execute_query(self, query):
        """Execute SQL query and return results."""
        try:
            connection_string = self.create_connection_string()
            self.logger.info("Connecting to EDW database...")
            
            with pyodbc.connect(connection_string, timeout=300) as conn:
                self.logger.info("Database connection established")
                
                cursor = conn.cursor()
                cursor.execute(query)
                
                # Get column names
                columns = [column[0] for column in cursor.description]
                
                # Fetch all results
                rows = cursor.fetchall()
                
                self.logger.info(f"Query executed successfully. Retrieved {len(rows)} rows")
                
                return columns, rows
                
        except pyodbc.Error as e:
            self.logger.error(f"Database error: {str(e)}")
            raise
        except Exception as e:
            self.logger.error(f"Error executing query: {str(e)}")
            raise

    def save_results_to_csv(self, columns, rows):
        """Save query results to CSV file."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.output_prefix}_{timestamp}.csv"
            filepath = self.output_dir / filename
            
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                
                # Write header
                writer.writerow(columns)
                
                # Write data rows
                for row in rows:
                    # Convert pyodbc.Row to list and handle None values
                    row_data = [str(value) if value is not None else '' for value in row]
                    writer.writerow(row_data)
            
            self.logger.info(f"Results saved to: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"Error saving results to CSV: {str(e)}")
            raise

    def run_query(self):
        """Main method to run the complete query process."""
        try:
            self.logger.info("Starting EDW query process")
            
            # Check for existing Kerberos ticket, authenticate if needed
            if not self.check_kerberos_ticket():
                if not self.authenticate_kerberos():
                    raise Exception("Kerberos authentication failed")
            
            # Load SQL query
            query = self.load_sql_query()
            
            # Execute query
            columns, rows = self.execute_query(query)
            
            # Save results
            if self.output_format.lower() == 'csv':
                output_file = self.save_results_to_csv(columns, rows)
                self.logger.info(f"Query completed successfully. Output: {output_file}")
                return output_file
            else:
                raise ValueError(f"Unsupported output format: {self.output_format}")
                
        except Exception as e:
            self.logger.error(f"Query process failed: {str(e)}")
            raise

def main():
    """Main entry point."""
    try:
        manager = EDWQueryManager()
        output_file = manager.run_query()
        print(f"Query completed successfully. Output saved to: {output_file}")
        return 0
        
    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        return 1

if __name__ == "__main__":
    sys.exit(main())
