# UW Enterprise Data Warehouse Query Tool

This Python application connects to the University of Washington Enterprise Data Warehouse using Kerberos authentication and executes SQL queries on an hourly schedule.

## Features

- **Kerberos Authentication**: Automatic authentication using UW NetID credentials
- **SQL Server Connectivity**: Connects to Microsoft SQL Server with encryption
- **Automated Scheduling**: Designed to run hourly via cron jobs
- **Comprehensive Logging**: Detailed logging for monitoring and debugging
- **CSV Output**: Query results saved in CSV format with timestamps
- **Error Handling**: Robust error handling and recovery mechanisms

## Prerequisites

### System Requirements
- macOS (tested on macOS 10.15+)
- Python 3.8 or higher
- Microsoft ODBC Driver 18 for SQL Server
- Kerberos client tools (kinit)

### Database Access
- UW NetID with EDW access permissions
- Network access to `edwpub.s.uw.edu`

## Installation

### 1. Clone or Download the Project
```bash
cd /path/to/your/project
# Files should include: edw_query.py, .env, requirements.txt, setup.sh, studentquery.sql
```

### 2. Run the Setup Script
```bash
chmod +x setup.sh
./setup.sh
```

### 3. Install Microsoft ODBC Driver (if not already installed)

**Option A: Using Homebrew (Recommended)**
```bash
brew tap microsoft/mssql-release https://github.com/Microsoft/homebrew-mssql-release
brew update
HOMEBREW_ACCEPT_EULA=Y brew install msodbcsql18 mssql-tools18
```

**Option B: Manual Download**
Download from: https://docs.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server

## Configuration

### Environment Variables (.env file)
The `.env` file contains all configuration settings:

```env
# Database Connection Settings
EDW_SERVER=edwpub.s.uw.edu
EDW_DATABASE=EDWPresentation
EDW_ENCRYPT=yes
EDW_TRUST_SERVER_CERTIFICATE=no

# Kerberos Authentication
UW_NETID=a_ssw_edw_access

# Query Settings
SQL_QUERY_FILE=studentquery.sql

# Output Settings
OUTPUT_FORMAT=csv
OUTPUT_DIRECTORY=./output
OUTPUT_FILENAME_PREFIX=student_data

# Logging Settings
LOG_LEVEL=INFO
LOG_FILE=./logs/edw_query.log
```

### SQL Query File
Place your SQL query in `studentquery.sql`. The current query retrieves student information from the UW Student Database.

## Usage

### Manual Execution
```bash
# Activate virtual environment
source venv/bin/activate

# Run the query
python3 edw_query.py
```

### Automated Hourly Execution

#### 1. Create a wrapper script for cron
Create `run_edw_query.sh`:
```bash
#!/bin/bash
cd /Users/<USER>/Desktop/edwcode
source venv/bin/activate
python3 edw_query.py
```

Make it executable:
```bash
chmod +x run_edw_query.sh
```

#### 2. Set up cron job
```bash
# Edit crontab
crontab -e

# Add this line to run every hour at minute 0
0 * * * * /Users/<USER>/Desktop/edwcode/run_edw_query.sh >> /Users/<USER>/Desktop/edwcode/logs/cron.log 2>&1
```

#### Alternative: Using launchd (macOS preferred method)
Create `com.uw.edw.query.plist` in `~/Library/LaunchAgents/`:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.uw.edw.query</string>
    <key>ProgramArguments</key>
    <array>
        <string>/Users/<USER>/Desktop/edwcode/run_edw_query.sh</string>
    </array>
    <key>StartInterval</key>
    <integer>3600</integer>
    <key>StandardOutPath</key>
    <string>/Users/<USER>/Desktop/edwcode/logs/launchd.log</string>
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/Desktop/edwcode/logs/launchd_error.log</string>
</dict>
</plist>
```

Load the job:
```bash
launchctl load ~/Library/LaunchAgents/com.uw.edw.query.plist
```

## Output

### CSV Files
Query results are saved as CSV files in the `output/` directory with timestamps:
- Format: `student_data_YYYYMMDD_HHMMSS.csv`
- Example: `student_data_20241216_143022.csv`

### Log Files
Detailed logs are saved in the `logs/` directory:
- `edw_query.log` - Application logs
- `cron.log` - Cron job execution logs (if using cron)
- `launchd.log` - LaunchAgent logs (if using launchd)

## Troubleshooting

### Common Issues

#### 1. Kerberos Authentication Fails
```bash
# Check if kinit is working
kinit a_ssw_edw_access

# Verify ticket
klist
```

#### 2. ODBC Driver Not Found
```bash
# Check installed drivers
odbcinst -q -d

# Should show "ODBC Driver 18 for SQL Server"
```

#### 3. Database Connection Issues
- Verify network connectivity to `edwpub.s.uw.edu`
- Check if your UW NetID has EDW access permissions
- Ensure you're on the UW network or connected via VPN

#### 4. Permission Issues
```bash
# Make scripts executable
chmod +x edw_query.py
chmod +x setup.sh
chmod +x run_edw_query.sh
```

### Monitoring

#### Check Recent Logs
```bash
# View recent application logs
tail -f logs/edw_query.log

# View recent cron logs
tail -f logs/cron.log
```

#### Verify Cron Job Status
```bash
# List active cron jobs
crontab -l

# Check system logs for cron activity
grep CRON /var/log/system.log
```

## Security Considerations

- The `.env` file contains sensitive configuration. Keep it secure and don't commit to version control.
- Kerberos tickets expire. The script automatically handles re-authentication.
- Use strong file permissions on the project directory.
- Regularly monitor logs for any security issues.

## Support

For issues related to:
- **Database access**: Contact UW IT or your database administrator
- **Kerberos authentication**: Contact UW IT Identity & Access Management
- **Script functionality**: Check logs and review configuration settings

## File Structure
```
edwcode/
├── edw_query.py          # Main application script
├── .env                  # Configuration file
├── requirements.txt      # Python dependencies
├── setup.sh             # Setup script
├── studentquery.sql     # SQL query file
├── README.md            # This file
├── venv/                # Python virtual environment
├── output/              # Query results (CSV files)
└── logs/                # Log files
```
