#!/bin/bash

# Setup script for UW EDW Query Scheduler
# Configures launchd to run queries hourly between 7AM-7PM Pacific time

echo "Setting up UW EDW Query Scheduler..."

# Define paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PLIST_FILE="$SCRIPT_DIR/com.uw.edw.query.plist"
LAUNCHAGENTS_DIR="$HOME/Library/LaunchAgents"
INSTALLED_PLIST="$LAUNCHAGENTS_DIR/com.uw.edw.query.plist"

# Create LaunchAgents directory if it doesn't exist
mkdir -p "$LAUNCHAGENTS_DIR"

# Check if already installed
if [ -f "$INSTALLED_PLIST" ]; then
    echo "Scheduler already installed. Unloading existing configuration..."
    launchctl unload "$INSTALLED_PLIST" 2>/dev/null || true
    rm "$INSTALLED_PLIST"
fi

# Copy plist file to LaunchAgents directory
echo "Installing scheduler configuration..."
cp "$PLIST_FILE" "$INSTALLED_PLIST"

# Set proper permissions
chmod 644 "$INSTALLED_PLIST"

# Load the launch agent
echo "Loading scheduler..."
launchctl load "$INSTALLED_PLIST"

# Verify installation
if launchctl list | grep -q "com.uw.edw.query"; then
    echo "✅ Scheduler installed successfully!"
    echo ""
    echo "📅 Schedule: Every hour from 7AM to 7PM Pacific time"
    echo "📁 Logs: $SCRIPT_DIR/logs/scheduler.log"
    echo "📁 LaunchD Logs: $SCRIPT_DIR/logs/launchd.log"
    echo "📁 Error Logs: $SCRIPT_DIR/logs/launchd_error.log"
    echo ""
    echo "To check status: launchctl list | grep com.uw.edw.query"
    echo "To uninstall: ./uninstall_scheduler.sh"
    echo ""
    echo "Next execution times (Pacific):"
    
    # Show next few execution times
    current_hour=$(TZ="America/Los_Angeles" date +%H)
    current_minute=$(TZ="America/Los_Angeles" date +%M)
    
    for hour in {7..19}; do
        if [ $hour -gt $current_hour ] || ([ $hour -eq $current_hour ] && [ $current_minute -lt 0 ]); then
            next_time=$(TZ="America/Los_Angeles" date -v${hour}H -v0M "+%I:%M %p %Z")
            echo "  - $next_time"
            break
        fi
    done
    
else
    echo "❌ Failed to install scheduler"
    exit 1
fi
