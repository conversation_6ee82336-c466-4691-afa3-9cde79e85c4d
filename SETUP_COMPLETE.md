# UW EDW Query Setup Complete! 🎉

Your Python solution for querying the University of Washington Enterprise Data Warehouse is now fully configured and ready to use.

## ✅ What's Been Set Up

### 1. **Complete Python Environment**
- Virtual environment created (`venv/`)
- Required packages installed:
  - `pyodbc` 5.2.0 (SQL Server connectivity)
  - `python-dotenv` 1.1.1 (configuration management)

### 2. **Database Connectivity**
- Microsoft ODBC Driver 18 for SQL Server installed
- UnixODBC installed and configured
- Connection tested and verified

### 3. **Kerberos Authentication**
- `kinit` available and configured
- UW NetID: `a_ssw_edw_access`
- Automatic ticket renewal built into the script

### 4. **Core Application Files**
- `edw_query.py` - Main application script
- `studentquery.sql` - Your SQL query (237 lines)
- `.env` - Configuration file
- `test_setup.py` - Setup verification script

### 5. **Supporting Files**
- `README.md` - Comprehensive documentation
- `requirements.txt` - Python dependencies
- `setup.sh` - Automated setup script
- `run_edw_query.sh` - Cron wrapper script

### 6. **Directory Structure**
```
edwcode/
├── edw_query.py          # Main application
├── studentquery.sql      # Your SQL query
├── .env                  # Configuration
├── README.md             # Documentation
├── requirements.txt      # Dependencies
├── setup.sh             # Setup script
├── run_edw_query.sh     # Cron wrapper
├── test_setup.py        # Verification script
├── venv/                # Python virtual environment
├── output/              # Query results (CSV files)
└── logs/                # Log files
```

## 🚀 Ready to Use

### Test the Setup
```bash
# Run verification
./venv/bin/python test_setup.py

# Test the main script (requires UW network/VPN)
./venv/bin/python edw_query.py
```

### Set Up Hourly Execution

#### Option 1: Using cron (Traditional)
```bash
# Edit crontab
crontab -e

# Add this line for hourly execution
0 * * * * /Users/<USER>/Desktop/edwcode/run_edw_query.sh >> /Users/<USER>/Desktop/edwcode/logs/cron.log 2>&1
```

#### Option 2: Using launchd (macOS Preferred)
Create `~/Library/LaunchAgents/com.uw.edw.query.plist`:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.uw.edw.query</string>
    <key>ProgramArguments</key>
    <array>
        <string>/Users/<USER>/Desktop/edwcode/run_edw_query.sh</string>
    </array>
    <key>StartInterval</key>
    <integer>3600</integer>
    <key>StandardOutPath</key>
    <string>/Users/<USER>/Desktop/edwcode/logs/launchd.log</string>
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/Desktop/edwcode/logs/launchd_error.log</string>
</dict>
</plist>
```

Then load it:
```bash
launchctl load ~/Library/LaunchAgents/com.uw.edw.query.plist
```

## 📊 Output

- **CSV Files**: Saved in `output/` directory with timestamps
  - Format: `student_data_YYYYMMDD_HHMMSS.csv`
  - Example: `student_data_20241216_143022.csv`

- **Log Files**: Saved in `logs/` directory
  - `edw_query.log` - Application logs
  - `cron.log` - Cron execution logs
  - `launchd.log` - LaunchAgent logs

## 🔧 Configuration

All settings are in the `.env` file:
- Database connection details
- Authentication settings
- Output preferences
- Logging configuration

## 📚 Documentation

See `README.md` for:
- Detailed usage instructions
- Troubleshooting guide
- Security considerations
- Advanced configuration options

## ⚠️ Important Notes

1. **Network Requirements**: You must be on the UW network or connected via VPN
2. **Authentication**: The script will prompt for your password when kinit runs
3. **Permissions**: Ensure your UW NetID has EDW access permissions
4. **Monitoring**: Check log files regularly for any issues

## 🎯 Next Steps

1. **Test the connection**: Run `./venv/bin/python edw_query.py` manually first
2. **Verify output**: Check that CSV files are generated correctly
3. **Set up scheduling**: Choose either cron or launchd for hourly execution
4. **Monitor logs**: Set up log rotation if needed for long-term use

Your UW EDW query solution is now ready for production use! 🚀
