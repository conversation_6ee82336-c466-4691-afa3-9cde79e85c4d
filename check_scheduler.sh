#!/bin/bash

# Status check script for UW EDW Query Scheduler

echo "UW EDW Query Scheduler Status"
echo "=============================="

# Check if scheduler is loaded
if launchctl list | grep -q "com.uw.edw.query"; then
    echo "✅ Scheduler Status: ACTIVE"
    
    # Get detailed status
    echo ""
    echo "Scheduler Details:"
    launchctl list com.uw.edw.query
    
else
    echo "❌ Scheduler Status: NOT RUNNING"
    echo ""
    echo "To install: ./setup_scheduler.sh"
    exit 1
fi

echo ""
echo "Current Time (Pacific): $(TZ='America/Los_Angeles' date)"

# Show next execution time
current_hour=$(TZ="America/Los_Angeles" date +%H)
current_minute=$(TZ="America/Los_Angeles" date +%M)

echo ""
echo "Next Scheduled Executions (Pacific Time):"

found_next=false
for hour in {7..19}; do
    if [ $hour -gt $current_hour ] || ([ $hour -eq $current_hour ] && [ $current_minute -lt 0 ]); then
        if [ "$found_next" = false ]; then
            next_time=$(TZ="America/Los_Angeles" date -j -f "%H" "$hour" "+%I:%M %p")
            echo "  Next: Today at $next_time"
            found_next=true
        fi
        time_str=$(TZ="America/Los_Angeles" date -j -f "%H" "$hour" "+%I:%M %p")
        echo "  - $time_str"
    fi
done

if [ "$found_next" = false ]; then
    echo "  Next: Tomorrow at 7:00 AM"
fi

echo ""
echo "Recent Log Entries:"
echo "==================="

# Show recent scheduler logs
if [ -f "logs/scheduler.log" ]; then
    echo "Last 5 scheduler entries:"
    tail -5 logs/scheduler.log
else
    echo "No scheduler log found yet."
fi

echo ""
echo "Recent Query Results:"
echo "===================="

# Show recent output files
if ls output/student_data_*.csv 1> /dev/null 2>&1; then
    echo "Recent output files:"
    ls -lt output/student_data_*.csv | head -3
else
    echo "No output files found yet."
fi

echo ""
echo "Log Files:"
echo "=========="
echo "  Scheduler: logs/scheduler.log"
echo "  LaunchD:   logs/launchd.log"
echo "  Errors:    logs/launchd_error.log"
echo "  App:       logs/edw_query.log"
