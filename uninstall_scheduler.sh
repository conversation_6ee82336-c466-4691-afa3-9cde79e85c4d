#!/bin/bash

# Uninstall script for UW EDW Query Scheduler
# Removes the launchd configuration

echo "Uninstalling UW EDW Query Scheduler..."

LAUNCHAGENTS_DIR="$HOME/Library/LaunchAgents"
INSTALLED_PLIST="$LAUNCHAGENTS_DIR/com.uw.edw.query.plist"

# Check if installed
if [ ! -f "$INSTALLED_PLIST" ]; then
    echo "Scheduler is not installed."
    exit 0
fi

# Unload the launch agent
echo "Unloading scheduler..."
launchctl unload "$INSTALLED_PLIST" 2>/dev/null || true

# Remove the plist file
echo "Removing configuration file..."
rm "$INSTALLED_PLIST"

# Verify removal
if ! launchctl list | grep -q "com.uw.edw.query"; then
    echo "✅ Scheduler uninstalled successfully!"
else
    echo "❌ Failed to uninstall scheduler completely"
    echo "You may need to restart your session or run:"
    echo "launchctl remove com.uw.edw.query"
fi
